/* ===== SAFENEST MODAL STYLES ===== */

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(26, 29, 58, 0.8);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10001;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: rgba(26, 29, 58, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 255, 255, 0.2);
    border: 1px solid rgba(0, 255, 255, 0.3);
    backdrop-filter: blur(20px);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    transform: scale(0.8);
    transition: all 0.3s ease;
}

.modal-overlay.show .modal-content {
    transform: scale(1);
}

.modal-header {
    background: linear-gradient(135deg, #00ffff 0%, #8a2be2 100%);
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: white;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.modal-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.modal-body {
    padding: 30px;
    color: white;
}

/* Get Started Modal */
.get-started-content h3 {
    text-align: center;
    margin-bottom: 25px;
    color: #00ffff;
    font-size: 1.4rem;
}

.path-options {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
}

.path-option {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(0, 255, 255, 0.3);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.path-option:hover {
    background: rgba(0, 255, 255, 0.1);
    border-color: #00ffff;
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 255, 255, 0.2);
}

.path-option i {
    font-size: 2.5rem;
    background: linear-gradient(135deg, #00ffff 0%, #8a2be2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 15px;
    display: block;
}

.path-option h4 {
    margin: 0 0 10px 0;
    color: #00ffff;
    font-size: 1.2rem;
}

.path-option p {
    margin: 0;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

/* Consultation Form */
.consultation-form {
    max-width: 100%;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #00ffff;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 10px;
    padding: 12px 15px;
    color: white;
    font-size: 14px;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #00ffff;
    background: rgba(0, 255, 255, 0.1);
    box-shadow: 0 0 0 3px rgba(0, 255, 255, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.form-group select option {
    background: #1a1d3a;
    color: white;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border: none;
    border-radius: 10px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, #00ffff 0%, #8a2be2 100%);
    color: white;
    width: 100%;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 255, 255, 0.3);
}

.btn-primary:active {
    transform: translateY(0);
}

/* Mobile Navigation Styles */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        left: 0;
        width: 100%;
        background: rgba(26, 29, 58, 0.95);
        backdrop-filter: blur(20px);
        border-top: 1px solid rgba(0, 255, 255, 0.2);
        padding: 20px;
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        flex-direction: column;
        gap: 0;
        z-index: 1000;
    }
    
    .nav-menu.mobile-open {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }
    
    .nav-dropdown {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .nav-dropdown-toggle {
        width: 100%;
        justify-content: space-between;
        padding: 15px;
        border-radius: 10px;
        background: rgba(255, 255, 255, 0.1);
    }
    
    .nav-dropdown-menu {
        position: static;
        width: 100%;
        background: rgba(255, 255, 255, 0.05);
        border: none;
        border-radius: 10px;
        margin-top: 10px;
        box-shadow: none;
        backdrop-filter: none;
    }
    
    .nav-link {
        width: 100%;
        justify-content: flex-start;
        padding: 15px;
        border-radius: 10px;
        background: rgba(255, 255, 255, 0.1);
        margin-bottom: 10px;
    }
    
    .mobile-menu-toggle {
        color: #ffffff !important;
    }
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(26, 29, 58, 0.95);
    color: white;
    padding: 15px 20px;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border-left: 4px solid #00ffff;
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    gap: 10px;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 10002;
    max-width: 350px;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.success {
    border-left-color: #00ff00;
}

.notification.error {
    border-left-color: #ff4444;
}

.notification.info {
    border-left-color: #00ffff;
}

.notification i {
    font-size: 1.2rem;
}

.notification.success i {
    color: #00ff00;
}

.notification.error i {
    color: #ff4444;
}

.notification.info i {
    color: #00ffff;
}

/* Responsive Design */
@media (max-width: 480px) {
    .modal-content {
        width: 95%;
        margin: 20px;
    }
    
    .modal-body {
        padding: 20px;
    }
    
    .path-options {
        gap: 10px;
    }
    
    .path-option {
        padding: 15px;
    }
    
    .path-option i {
        font-size: 2rem;
    }
    
    .notification {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }
}
