{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="dashboard-container">
    <div class="dashboard-header">
        <h1 class="page-title">
            <i class="fas fa-home"></i>
            SafeNest - AI-Powered Smart Home & Architecture Platform
        </h1>
        <p class="subtitle">Your Complete Smart Home and Architecture Design Solution</p>
        <div class="weather-widget">
            <div class="weather-info">
                <span class="temperature">{{ smart_home_stats.avg_temperature }}°C</span>
                <i class="fas fa-sun weather-icon"></i>
            </div>
        </div>
    </div>

    <!-- Featured Services Section -->
    <div class="section-header">
        <h2><i class="fas fa-star"></i> Featured Services</h2>
    </div>
    <div class="services-grid">
        {% for service in featured_services %}
        <div class="service-card {{ service.color }}">
            <div class="service-icon">
                <i class="{{ service.icon }}"></i>
            </div>
            <div class="service-content">
                <h3>{{ service.name }}</h3>
                <p>{{ service.description }}</p>
                <a href="{{ service.url }}" class="service-btn">Get Started</a>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Smart Home IoT Stats -->
    <div class="section-header">
        <h2><i class="fas fa-home"></i> Smart Home IoT Dashboard</h2>
    </div>

    <div class="stats-grid">
        <div class="stat-card smart-home">
            <div class="stat-icon">
                <i class="fas fa-microchip"></i>
            </div>
            <div class="stat-content">
                <h3>{{ smart_home_stats.active_devices }}</h3>
                <p>Active Devices</p>
            </div>
        </div>

        <div class="stat-card smart-home">
            <div class="stat-icon">
                <i class="fas fa-shield-alt"></i>
            </div>
            <div class="stat-content">
                <h3>{{ smart_home_stats.security_status }}</h3>
                <p>Security Status</p>
            </div>
        </div>

        <div class="stat-card smart-home">
            <div class="stat-icon">
                <i class="fas fa-bolt"></i>
            </div>
            <div class="stat-content">
                <h3>{{ smart_home_stats.energy_usage }} kWh</h3>
                <p>Energy Usage</p>
            </div>
        </div>

        <div class="stat-card smart-home">
            <div class="stat-icon">
                <i class="fas fa-cogs"></i>
            </div>
            <div class="stat-content">
                <h3>{{ smart_home_stats.automation_rules }}</h3>
                <p>Automation Rules</p>
            </div>
        </div>
    </div>

    <!-- AI Architecture Stats -->
    <div class="section-header">
        <h2><i class="fas fa-cube"></i> AI Architecture & Design</h2>
    </div>
    <div class="stats-grid">
        <div class="stat-card architecture">
            <div class="stat-icon">
                <i class="fas fa-project-diagram"></i>
            </div>
            <div class="stat-content">
                <h3>{{ architecture_stats.total_projects }}</h3>
                <p>Total Projects</p>
            </div>
        </div>

        <div class="stat-card architecture">
            <div class="stat-icon">
                <i class="fas fa-magic"></i>
            </div>
            <div class="stat-content">
                <h3>{{ architecture_stats.ai_generations }}</h3>
                <p>AI Generations</p>
            </div>
        </div>

        <div class="stat-card architecture">
            <div class="stat-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-content">
                <h3>{{ architecture_stats.featured_architects }}</h3>
                <p>Featured Architects</p>
            </div>
        </div>

        <div class="stat-card architecture">
            <div class="stat-icon">
                <i class="fas fa-calculator"></i>
            </div>
            <div class="stat-content">
                <h3>{{ architecture_stats.material_estimates }}</h3>
                <p>Material Estimates</p>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="section-header">
        <h2><i class="fas fa-clock"></i> Recent Activity</h2>
    </div>
    <div class="activity-section">
        <div class="activity-list">
            {% for activity in recent_activity %}
            <div class="activity-item {{ activity.type }}">
                <div class="activity-icon">
                    {% if activity.type == 'ai_generation' %}
                        <i class="fas fa-magic"></i>
                    {% elif activity.type == 'automation' %}
                        <i class="fas fa-cogs"></i>
                    {% elif activity.type == 'consultation' %}
                        <i class="fas fa-comments"></i>
                    {% elif activity.type == 'energy' %}
                        <i class="fas fa-bolt"></i>
                    {% elif activity.type == 'security' %}
                        <i class="fas fa-shield-alt"></i>
                    {% endif %}
                </div>
                <div class="activity-content">
                    <p>{{ activity.description }}</p>
                    <small>{{ activity.time }}</small>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="section-header">
        <h2><i class="fas fa-rocket"></i> Quick Actions</h2>
    </div>
    <div class="quick-actions-grid">
        <button class="action-btn primary" onclick="window.location.href='/architecture/generator/'">
            <i class="fas fa-cube"></i>
            <span>Generate AI Architecture</span>
        </button>
        <button class="action-btn success" onclick="window.location.href='/automation/voice/'">
            <i class="fas fa-microphone"></i>
            <span>Voice Control</span>
        </button>
        <button class="action-btn warning" onclick="window.location.href='/energy/calculator/'">
            <i class="fas fa-calculator"></i>
            <span>Energy Calculator</span>
        </button>
        <button class="action-btn info" onclick="window.location.href='/materials/'">
            <i class="fas fa-hammer"></i>
            <span>Material Estimation</span>
        </button>
        <button class="action-btn secondary" onclick="window.location.href='/architects/'">
            <i class="fas fa-users"></i>
            <span>Find Architects</span>
        </button>
        <button class="action-btn danger" onclick="window.location.href='/consultation/'">
            <i class="fas fa-comments"></i>
            <span>Live Consultation</span>
        </button>
    </div>
</div>

<style>
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.service-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
}

.service-card.primary { border-left: 4px solid #007bff; }
.service-card.success { border-left: 4px solid #28a745; }
.service-card.warning { border-left: 4px solid #ffc107; }
.service-card.info { border-left: 4px solid #17a2b8; }
.service-card.secondary { border-left: 4px solid #6c757d; }
.service-card.danger { border-left: 4px solid #dc3545; }

.service-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
}

.service-btn {
    display: inline-block;
    background: #007bff;
    color: white;
    padding: 8px 16px;
    border-radius: 5px;
    text-decoration: none;
    margin-top: 10px;
}

.section-header {
    margin: 40px 0 20px 0;
}

.section-header h2 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card.smart-home {
    border-left: 4px solid #28a745;
}

.stat-card.architecture {
    border-left: 4px solid #007bff;
}

.activity-section {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #eee;
}

.activity-icon {
    margin-right: 15px;
    font-size: 1.5rem;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 40px;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    border: none;
    border-radius: 10px;
    color: white;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.action-btn:hover {
    transform: translateY(-3px);
}

.action-btn.primary { background: #007bff; }
.action-btn.success { background: #28a745; }
.action-btn.warning { background: #ffc107; color: #333; }
.action-btn.info { background: #17a2b8; }
.action-btn.secondary { background: #6c757d; }
.action-btn.danger { background: #dc3545; }

.action-btn i {
    font-size: 2rem;
    margin-bottom: 10px;
}
</style>

<script>
// Add any JavaScript functionality here
console.log('SafeNest Dashboard Loaded');
</script>
{% endblock %}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// WebSocket for real-time updates
const socket = new WebSocket('ws://localhost:8000/ws/dashboard/');

socket.onmessage = function(e) {
    const data = JSON.parse(e.data);
    updateDashboard(data);
};

// Initialize charts
const ctx = document.getElementById('energyChart').getContext('2d');
const energyChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        datasets: [{
            label: 'Energy Usage (kWh)',
            data: [12, 19, 3, 5, 2, 3, 9],
            borderColor: '#6366f1',
            backgroundColor: 'rgba(99, 102, 241, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                labels: {
                    color: 'var(--text-primary)'
                }
            }
        },
        scales: {
            y: {
                ticks: {
                    color: 'var(--text-secondary)'
                },
                grid: {
                    color: 'var(--border)'
                }
            },
            x: {
                ticks: {
                    color: 'var(--text-secondary)'
                },
                grid: {
                    color: 'var(--border)'
                }
            }
        }
    }
});

function toggleAllLights() {
    fetch('/api/devices/lights/toggle/', {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCookie('csrftoken'),
            'Content-Type': 'application/json'
        }
    });
}

function sendMessage() {
    const input = document.getElementById('chatInput');
    const message = input.value.trim();
    if (message) {
        addChatMessage('user', message);
        input.value = '';
        
        fetch('/api/ai/chat/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({message: message})
        })
        .then(response => response.json())
        .then(data => {
            addChatMessage('ai', data.response);
        });
    }
}

function addChatMessage(sender, message) {
    const chatMessages = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `chat-message ${sender}`;
    messageDiv.innerHTML = `
        <div class="message-content">${message}</div>
        <div class="message-time">${new Date().toLocaleTimeString()}</div>
    `;
    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}
</script>
{% endblock %}