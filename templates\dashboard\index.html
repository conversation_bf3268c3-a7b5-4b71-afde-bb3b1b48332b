{% extends 'base.html' %}

{% block content %}
<div style="padding: 40px 20px; max-width: 1400px; margin: 0 auto;">
    <!-- Hero Section -->
    <div style="text-align: center; margin-bottom: 60px; background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px); border-radius: 20px; padding: 60px 40px; border: 1px solid rgba(255, 255, 255, 0.2);">
        <h1 style="color: white; margin-bottom: 20px; font-size: 3rem; font-weight: 700; text-shadow: 0 2px 10px rgba(0,0,0,0.3);">
            <i class="fas fa-home" style="margin-right: 15px; color: #FFD700;"></i>
            SafeNest
        </h1>
        <h2 style="color: rgba(255, 255, 255, 0.9); margin-bottom: 15px; font-size: 1.5rem; font-weight: 500;">
            AI-Powered Smart Home & Architecture Platform
        </h2>
        <p style="color: rgba(255, 255, 255, 0.8); font-size: 1.2rem; max-width: 600px; margin: 0 auto;">
            Your Complete Smart Home and Architecture Design Solution
        </p>
    </div>

    <!-- Featured Services Section -->
    <div style="margin: 40px 0 30px 0;">
        <h2 style="color: white; font-size: 2rem; font-weight: 600; text-align: center; margin-bottom: 30px; text-shadow: 0 2px 10px rgba(0,0,0,0.3);">
            <i class="fas fa-star" style="color: #FFD700; margin-right: 10px;"></i> Featured Services
        </h2>
    </div>

    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)); gap: 25px; margin-bottom: 50px;">
        {% for service in featured_services %}
        <div style="background: rgba(255, 255, 255, 0.95); border-radius: 15px; padding: 30px; box-shadow: 0 8px 32px rgba(0,0,0,0.1); border: 1px solid rgba(255, 255, 255, 0.2); transition: all 0.3s ease; backdrop-filter: blur(10px);"
             onmouseover="this.style.transform='translateY(-8px)'; this.style.boxShadow='0 12px 40px rgba(0,0,0,0.15)'"
             onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 8px 32px rgba(0,0,0,0.1)'">
            <div style="font-size: 3rem; margin-bottom: 20px; text-align: center;">
                <i class="{{ service.icon }}" style="color: #667eea;"></i>
            </div>
            <div style="text-align: center;">
                <h3 style="color: #333; font-size: 1.3rem; font-weight: 600; margin-bottom: 15px;">{{ service.name }}</h3>
                <p style="color: #666; margin-bottom: 20px; line-height: 1.6;">{{ service.description }}</p>
                <a href="{{ service.url }}" style="display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 24px; border-radius: 25px; text-decoration: none; font-weight: 500; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);"
                   onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(102, 126, 234, 0.4)'"
                   onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(102, 126, 234, 0.3)'">
                   Get Started
                </a>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Smart Home IoT Stats -->
    <div style="margin: 50px 0 30px 0;">
        <h2 style="color: white; font-size: 2rem; font-weight: 600; text-align: center; margin-bottom: 30px; text-shadow: 0 2px 10px rgba(0,0,0,0.3);">
            <i class="fas fa-home" style="color: #28a745; margin-right: 10px;"></i> Smart Home IoT Dashboard
        </h2>
    </div>

    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 25px; margin-bottom: 50px;">
        <div style="background: rgba(255, 255, 255, 0.95); border-radius: 15px; padding: 25px; box-shadow: 0 8px 32px rgba(0,0,0,0.1); text-align: center; border-left: 4px solid #28a745; backdrop-filter: blur(10px); transition: all 0.3s ease;"
             onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 12px 40px rgba(0,0,0,0.15)'"
             onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 8px 32px rgba(0,0,0,0.1)'">
            <div style="font-size: 2.5rem; margin-bottom: 15px; color: #28a745;">
                <i class="fas fa-microchip"></i>
            </div>
            <div>
                <h3 style="color: #333; font-size: 2rem; font-weight: 700; margin-bottom: 5px;">{{ smart_home_stats.active_devices }}</h3>
                <p style="color: #666; font-weight: 500;">Active Devices</p>
            </div>
        </div>

        <div style="background: rgba(255, 255, 255, 0.95); border-radius: 15px; padding: 25px; box-shadow: 0 8px 32px rgba(0,0,0,0.1); text-align: center; border-left: 4px solid #28a745; backdrop-filter: blur(10px); transition: all 0.3s ease;"
             onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 12px 40px rgba(0,0,0,0.15)'"
             onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 8px 32px rgba(0,0,0,0.1)'">
            <div style="font-size: 2.5rem; margin-bottom: 15px; color: #28a745;">
                <i class="fas fa-shield-alt"></i>
            </div>
            <div>
                <h3 style="color: #333; font-size: 2rem; font-weight: 700; margin-bottom: 5px;">{{ smart_home_stats.security_status }}</h3>
                <p style="color: #666; font-weight: 500;">Security Status</p>
            </div>
        </div>

        <div style="background: rgba(255, 255, 255, 0.95); border-radius: 15px; padding: 25px; box-shadow: 0 8px 32px rgba(0,0,0,0.1); text-align: center; border-left: 4px solid #28a745; backdrop-filter: blur(10px); transition: all 0.3s ease;"
             onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 12px 40px rgba(0,0,0,0.15)'"
             onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 8px 32px rgba(0,0,0,0.1)'">
            <div style="font-size: 2.5rem; margin-bottom: 15px; color: #28a745;">
                <i class="fas fa-bolt"></i>
            </div>
            <div>
                <h3 style="color: #333; font-size: 2rem; font-weight: 700; margin-bottom: 5px;">{{ smart_home_stats.energy_usage }} kWh</h3>
                <p style="color: #666; font-weight: 500;">Energy Usage</p>
            </div>
        </div>

        <div style="background: rgba(255, 255, 255, 0.95); border-radius: 15px; padding: 25px; box-shadow: 0 8px 32px rgba(0,0,0,0.1); text-align: center; border-left: 4px solid #28a745; backdrop-filter: blur(10px); transition: all 0.3s ease;"
             onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 12px 40px rgba(0,0,0,0.15)'"
             onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 8px 32px rgba(0,0,0,0.1)'">
            <div style="font-size: 2.5rem; margin-bottom: 15px; color: #28a745;">
                <i class="fas fa-cogs"></i>
            </div>
            <div>
                <h3 style="color: #333; font-size: 2rem; font-weight: 700; margin-bottom: 5px;">{{ smart_home_stats.automation_rules }}</h3>
                <p style="color: #666; font-weight: 500;">Automation Rules</p>
            </div>
        </div>
    </div>

    <!-- AI Architecture Stats -->
    <div style="margin: 40px 0 20px 0;">
        <h2 style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px;">
            <i class="fas fa-cube"></i> AI Architecture & Design
        </h2>
    </div>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
        <div style="background: white; border-radius: 10px; padding: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center; border-left: 4px solid #007bff;">
            <div style="font-size: 2rem; margin-bottom: 10px; color: #007bff;">
                <i class="fas fa-project-diagram"></i>
            </div>
            <div>
                <h3>{{ architecture_stats.total_projects }}</h3>
                <p>Total Projects</p>
            </div>
        </div>

        <div style="background: white; border-radius: 10px; padding: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center; border-left: 4px solid #007bff;">
            <div style="font-size: 2rem; margin-bottom: 10px; color: #007bff;">
                <i class="fas fa-magic"></i>
            </div>
            <div>
                <h3>{{ architecture_stats.ai_generations }}</h3>
                <p>AI Generations</p>
            </div>
        </div>

        <div style="background: white; border-radius: 10px; padding: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center; border-left: 4px solid #007bff;">
            <div style="font-size: 2rem; margin-bottom: 10px; color: #007bff;">
                <i class="fas fa-users"></i>
            </div>
            <div>
                <h3>{{ architecture_stats.featured_architects }}</h3>
                <p>Featured Architects</p>
            </div>
        </div>

        <div style="background: white; border-radius: 10px; padding: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center; border-left: 4px solid #007bff;">
            <div style="font-size: 2rem; margin-bottom: 10px; color: #007bff;">
                <i class="fas fa-calculator"></i>
            </div>
            <div>
                <h3>{{ architecture_stats.material_estimates }}</h3>
                <p>Material Estimates</p>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div style="margin: 40px 0 20px 0;">
        <h2 style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px;">
            <i class="fas fa-clock"></i> Recent Activity
        </h2>
    </div>
    
    <div style="background: white; border-radius: 10px; padding: 20px; margin-bottom: 30px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
        <div>
            {% for activity in recent_activity %}
            <div style="display: flex; align-items: center; padding: 15px; border-bottom: 1px solid #eee;">
                <div style="margin-right: 15px; font-size: 1.5rem; color: #007bff;">
                    {% if activity.type == 'ai_generation' %}
                        <i class="fas fa-magic"></i>
                    {% elif activity.type == 'automation' %}
                        <i class="fas fa-cogs"></i>
                    {% elif activity.type == 'consultation' %}
                        <i class="fas fa-comments"></i>
                    {% elif activity.type == 'energy' %}
                        <i class="fas fa-bolt"></i>
                    {% elif activity.type == 'security' %}
                        <i class="fas fa-shield-alt"></i>
                    {% endif %}
                </div>
                <div>
                    <p>{{ activity.description }}</p>
                    <small>{{ activity.time }}</small>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Quick Actions -->
    <div style="margin: 40px 0 20px 0;">
        <h2 style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px;">
            <i class="fas fa-rocket"></i> Quick Actions
        </h2>
    </div>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 40px;">
        <button style="display: flex; flex-direction: column; align-items: center; padding: 20px; border: none; border-radius: 10px; color: white; cursor: pointer; background: #007bff;" onclick="window.location.href='/architecture/'">
            <i class="fas fa-cube" style="font-size: 2rem; margin-bottom: 10px;"></i>
            <span>Generate AI Architecture</span>
        </button>
        <button style="display: flex; flex-direction: column; align-items: center; padding: 20px; border: none; border-radius: 10px; color: white; cursor: pointer; background: #28a745;" onclick="window.location.href='/automation/'">
            <i class="fas fa-microphone" style="font-size: 2rem; margin-bottom: 10px;"></i>
            <span>Voice Control</span>
        </button>
        <button style="display: flex; flex-direction: column; align-items: center; padding: 20px; border: none; border-radius: 10px; color: #333; cursor: pointer; background: #ffc107;" onclick="window.location.href='/energy/'">
            <i class="fas fa-calculator" style="font-size: 2rem; margin-bottom: 10px;"></i>
            <span>Energy Calculator</span>
        </button>
        <button style="display: flex; flex-direction: column; align-items: center; padding: 20px; border: none; border-radius: 10px; color: white; cursor: pointer; background: #17a2b8;" onclick="window.location.href='/materials/'">
            <i class="fas fa-hammer" style="font-size: 2rem; margin-bottom: 10px;"></i>
            <span>Material Estimation</span>
        </button>
        <button style="display: flex; flex-direction: column; align-items: center; padding: 20px; border: none; border-radius: 10px; color: white; cursor: pointer; background: #6c757d;" onclick="window.location.href='/architects/'">
            <i class="fas fa-users" style="font-size: 2rem; margin-bottom: 10px;"></i>
            <span>Find Architects</span>
        </button>
        <button style="display: flex; flex-direction: column; align-items: center; padding: 20px; border: none; border-radius: 10px; color: white; cursor: pointer; background: #dc3545;" onclick="window.location.href='/consultation/'">
            <i class="fas fa-comments" style="font-size: 2rem; margin-bottom: 10px;"></i>
            <span>Live Consultation</span>
        </button>
    </div>
</div>

<script>
console.log('SafeNest Dashboard Loaded');
</script>
{% endblock %}
