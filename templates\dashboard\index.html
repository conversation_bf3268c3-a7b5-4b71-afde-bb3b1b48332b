{% extends 'base.html' %}

{% block content %}
<div style="padding: 20px; max-width: 1200px; margin: 0 auto;">
    <div style="text-align: center; margin-bottom: 40px;">
        <h1 style="color: #333; margin-bottom: 10px;">
            <i class="fas fa-home"></i>
            SafeNest - AI-Powered Smart Home & Architecture Platform
        </h1>
        <p style="color: #666; font-size: 1.1rem;">Your Complete Smart Home and Architecture Design Solution</p>
    </div>

    <!-- Featured Services Section -->
    <div style="margin: 40px 0 20px 0;">
        <h2 style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px;">
            <i class="fas fa-star"></i> Featured Services
        </h2>
    </div>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 40px;">
        {% for service in featured_services %}
        <div style="background: white; border-radius: 10px; padding: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-left: 4px solid #007bff;">
            <div style="font-size: 2.5rem; margin-bottom: 15px; color: #007bff;">
                <i class="{{ service.icon }}"></i>
            </div>
            <div>
                <h3>{{ service.name }}</h3>
                <p>{{ service.description }}</p>
                <a href="{{ service.url }}" style="display: inline-block; background: #007bff; color: white; padding: 8px 16px; border-radius: 5px; text-decoration: none; margin-top: 10px;">Get Started</a>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Smart Home IoT Stats -->
    <div style="margin: 40px 0 20px 0;">
        <h2 style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px;">
            <i class="fas fa-home"></i> Smart Home IoT Dashboard
        </h2>
    </div>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
        <div style="background: white; border-radius: 10px; padding: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center; border-left: 4px solid #28a745;">
            <div style="font-size: 2rem; margin-bottom: 10px; color: #007bff;">
                <i class="fas fa-microchip"></i>
            </div>
            <div>
                <h3>{{ smart_home_stats.active_devices }}</h3>
                <p>Active Devices</p>
            </div>
        </div>

        <div style="background: white; border-radius: 10px; padding: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center; border-left: 4px solid #28a745;">
            <div style="font-size: 2rem; margin-bottom: 10px; color: #007bff;">
                <i class="fas fa-shield-alt"></i>
            </div>
            <div>
                <h3>{{ smart_home_stats.security_status }}</h3>
                <p>Security Status</p>
            </div>
        </div>

        <div style="background: white; border-radius: 10px; padding: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center; border-left: 4px solid #28a745;">
            <div style="font-size: 2rem; margin-bottom: 10px; color: #007bff;">
                <i class="fas fa-bolt"></i>
            </div>
            <div>
                <h3>{{ smart_home_stats.energy_usage }} kWh</h3>
                <p>Energy Usage</p>
            </div>
        </div>

        <div style="background: white; border-radius: 10px; padding: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center; border-left: 4px solid #28a745;">
            <div style="font-size: 2rem; margin-bottom: 10px; color: #007bff;">
                <i class="fas fa-cogs"></i>
            </div>
            <div>
                <h3>{{ smart_home_stats.automation_rules }}</h3>
                <p>Automation Rules</p>
            </div>
        </div>
    </div>

    <!-- AI Architecture Stats -->
    <div style="margin: 40px 0 20px 0;">
        <h2 style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px;">
            <i class="fas fa-cube"></i> AI Architecture & Design
        </h2>
    </div>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
        <div style="background: white; border-radius: 10px; padding: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center; border-left: 4px solid #007bff;">
            <div style="font-size: 2rem; margin-bottom: 10px; color: #007bff;">
                <i class="fas fa-project-diagram"></i>
            </div>
            <div>
                <h3>{{ architecture_stats.total_projects }}</h3>
                <p>Total Projects</p>
            </div>
        </div>

        <div style="background: white; border-radius: 10px; padding: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center; border-left: 4px solid #007bff;">
            <div style="font-size: 2rem; margin-bottom: 10px; color: #007bff;">
                <i class="fas fa-magic"></i>
            </div>
            <div>
                <h3>{{ architecture_stats.ai_generations }}</h3>
                <p>AI Generations</p>
            </div>
        </div>

        <div style="background: white; border-radius: 10px; padding: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center; border-left: 4px solid #007bff;">
            <div style="font-size: 2rem; margin-bottom: 10px; color: #007bff;">
                <i class="fas fa-users"></i>
            </div>
            <div>
                <h3>{{ architecture_stats.featured_architects }}</h3>
                <p>Featured Architects</p>
            </div>
        </div>

        <div style="background: white; border-radius: 10px; padding: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center; border-left: 4px solid #007bff;">
            <div style="font-size: 2rem; margin-bottom: 10px; color: #007bff;">
                <i class="fas fa-calculator"></i>
            </div>
            <div>
                <h3>{{ architecture_stats.material_estimates }}</h3>
                <p>Material Estimates</p>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div style="margin: 40px 0 20px 0;">
        <h2 style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px;">
            <i class="fas fa-clock"></i> Recent Activity
        </h2>
    </div>
    
    <div style="background: white; border-radius: 10px; padding: 20px; margin-bottom: 30px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
        <div>
            {% for activity in recent_activity %}
            <div style="display: flex; align-items: center; padding: 15px; border-bottom: 1px solid #eee;">
                <div style="margin-right: 15px; font-size: 1.5rem; color: #007bff;">
                    {% if activity.type == 'ai_generation' %}
                        <i class="fas fa-magic"></i>
                    {% elif activity.type == 'automation' %}
                        <i class="fas fa-cogs"></i>
                    {% elif activity.type == 'consultation' %}
                        <i class="fas fa-comments"></i>
                    {% elif activity.type == 'energy' %}
                        <i class="fas fa-bolt"></i>
                    {% elif activity.type == 'security' %}
                        <i class="fas fa-shield-alt"></i>
                    {% endif %}
                </div>
                <div>
                    <p>{{ activity.description }}</p>
                    <small>{{ activity.time }}</small>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Quick Actions -->
    <div style="margin: 40px 0 20px 0;">
        <h2 style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px;">
            <i class="fas fa-rocket"></i> Quick Actions
        </h2>
    </div>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 40px;">
        <button style="display: flex; flex-direction: column; align-items: center; padding: 20px; border: none; border-radius: 10px; color: white; cursor: pointer; background: #007bff;" onclick="window.location.href='/architecture/'">
            <i class="fas fa-cube" style="font-size: 2rem; margin-bottom: 10px;"></i>
            <span>Generate AI Architecture</span>
        </button>
        <button style="display: flex; flex-direction: column; align-items: center; padding: 20px; border: none; border-radius: 10px; color: white; cursor: pointer; background: #28a745;" onclick="window.location.href='/automation/'">
            <i class="fas fa-microphone" style="font-size: 2rem; margin-bottom: 10px;"></i>
            <span>Voice Control</span>
        </button>
        <button style="display: flex; flex-direction: column; align-items: center; padding: 20px; border: none; border-radius: 10px; color: #333; cursor: pointer; background: #ffc107;" onclick="window.location.href='/energy/'">
            <i class="fas fa-calculator" style="font-size: 2rem; margin-bottom: 10px;"></i>
            <span>Energy Calculator</span>
        </button>
        <button style="display: flex; flex-direction: column; align-items: center; padding: 20px; border: none; border-radius: 10px; color: white; cursor: pointer; background: #17a2b8;" onclick="window.location.href='/materials/'">
            <i class="fas fa-hammer" style="font-size: 2rem; margin-bottom: 10px;"></i>
            <span>Material Estimation</span>
        </button>
        <button style="display: flex; flex-direction: column; align-items: center; padding: 20px; border: none; border-radius: 10px; color: white; cursor: pointer; background: #6c757d;" onclick="window.location.href='/architects/'">
            <i class="fas fa-users" style="font-size: 2rem; margin-bottom: 10px;"></i>
            <span>Find Architects</span>
        </button>
        <button style="display: flex; flex-direction: column; align-items: center; padding: 20px; border: none; border-radius: 10px; color: white; cursor: pointer; background: #dc3545;" onclick="window.location.href='/consultation/'">
            <i class="fas fa-comments" style="font-size: 2rem; margin-bottom: 10px;"></i>
            <span>Live Consultation</span>
        </button>
    </div>
</div>

<script>
console.log('SafeNest Dashboard Loaded');
</script>
{% endblock %}
