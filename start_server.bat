@echo off
echo Starting SafeNest Django Server...
echo ====================================

echo [INFO] Checking Django setup...
python manage.py check
if %errorlevel% neq 0 (
    echo [ERROR] Django check failed
    pause
    exit /b 1
)

echo [OK] Django check passed

echo [INFO] Starting server on port 8000...
python manage.py runserver 127.0.0.1:8000
if %errorlevel% neq 0 (
    echo [WARNING] Port 8000 failed, trying 8001...
    python manage.py runserver 127.0.0.1:8001
    if %errorlevel% neq 0 (
        echo [WARNING] Port 8001 failed, trying 8002...
        python manage.py runserver 127.0.0.1:8002
    )
)

pause
