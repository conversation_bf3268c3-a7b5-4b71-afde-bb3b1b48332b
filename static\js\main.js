// Theme management
function toggleTheme() {
    const body = document.body;
    const currentTheme = body.getAttribute('data-theme');
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    
    body.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);
    
    // Update theme toggle icon
    const icon = document.querySelector('.theme-toggle i');
    icon.className = newTheme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
}

// Initialize theme
document.addEventListener('DOMContentLoaded', function() {
    const savedTheme = localStorage.getItem('theme') || 'dark';
    document.body.setAttribute('data-theme', savedTheme);
    
    // Voice recognition for AI assistant
    if ('webkitSpeechRecognition' in window) {
        const recognition = new webkitSpeechRecognition();
        recognition.continuous = false;
        recognition.interimResults = false;
        recognition.lang = 'en-US';
        
        recognition.onresult = function(event) {
            const transcript = event.results[0][0].transcript;
            document.getElementById('chatInput').value = transcript;
            sendMessage();
        };
        
        // Add voice button to chat
        const voiceBtn = document.createElement('button');
        voiceBtn.innerHTML = '<i class="fas fa-microphone"></i>';
        voiceBtn.onclick = () => recognition.start();
        document.querySelector('.chat-input').appendChild(voiceBtn);
    }
    
    // Gesture recognition (basic implementation)
    let gestureStartX, gestureStartY;
    
    document.addEventListener('touchstart', function(e) {
        gestureStartX = e.touches[0].clientX;
        gestureStartY = e.touches[0].clientY;
    });
    
    document.addEventListener('touchend', function(e) {
        const gestureEndX = e.changedTouches[0].clientX;
        const gestureEndY = e.changedTouches[0].clientY;
        
        const deltaX = gestureEndX - gestureStartX;
        const deltaY = gestureEndY - gestureStartY;
        
        // Swipe gestures
        if (Math.abs(deltaX) > 100) {
            if (deltaX > 0) {
                // Swipe right - toggle lights
                toggleAllLights();
            } else {
                // Swipe left - activate security
                activateSecurityMode();
            }
        }
    });
});

// Utility functions
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Notification system
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}"></i>
        <span>${message}</span>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

// Device control functions
function activateSecurityMode() {
    fetch('/api/security/activate/', {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCookie('csrftoken'),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        showNotification('Security mode activated', 'success');
    });
}

function adjustTemperature() {
    const temp = prompt('Set temperature (°C):');
    if (temp) {
        fetch('/api/devices/thermostat/set/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({temperature: parseInt(temp)})
        })
        .then(response => response.json())
        .then(data => {
            showNotification(`Temperature set to ${temp}°C`, 'success');
        });
    }
}