from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import api_view
from rest_framework.response import Response
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
import json

def index(request):
    """Main dashboard view"""
    context = {
        'title': 'SafeNest Dashboard',
        'active_devices': 0,  # Will be calculated from Device model when available
        'avg_temperature': 24,
        'energy_usage': 156.7,
    }
    return render(request, 'dashboard/index.html', context)

def dashboard_view(request):
    context = {
        'active_devices': 0,  # Device.objects.filter(is_online=True).count() when model is available
        'avg_temperature': 24,  # Calculate from sensors
        'energy_usage': 156.7,  # Calculate from smart meters
    }
    return render(request, 'dashboard/index.html', context)

@api_view(['POST'])
def toggle_lights(request):
    # lights = Device.objects.filter(device_type='light') # Uncomment when Device model is available
    # for light in lights:
    #     current_status = light.status.get('on', False)
    #     light.status['on'] = not current_status
    #     light.save()
    
    # Send real-time update
    channel_layer = get_channel_layer()
    async_to_sync(channel_layer.group_send)(
        'dashboard',
        {
            'type': 'device_update',
            'message': 'Lights toggled'
        }
    )
    
    return Response({'status': 'success'})

@api_view(['POST'])
def ai_chat(request):
    message = request.data.get('message')
    
    # Simple AI responses (integrate with actual AI service)
    responses = {
        'temperature': 'The current temperature is 24°C. Would you like me to adjust it?',
        'lights': 'I can control your lights. Say "turn on lights" or "turn off lights".',
        'security': 'Your home security is active. All sensors are functioning normally.',
        'default': 'I\'m here to help with your smart home. Try asking about temperature, lights, or security.'
    }
    
    response_text = responses.get('default')
    for key, value in responses.items():
        if key in message.lower():
            response_text = value
            break
    
    return Response({'response': response_text})