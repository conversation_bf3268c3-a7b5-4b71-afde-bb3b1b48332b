#!/usr/bin/env python
"""
Simple Django server starter
"""

import os
import sys
import django
from django.core.management import execute_from_command_line

if __name__ == '__main__':
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'safenest.settings')
    
    print("Starting SafeNest Django Server...")
    print("=" * 40)
    
    try:
        # Try different ports
        ports = [8000, 8001, 8002, 8080]
        
        for port in ports:
            print(f"Trying port {port}...")
            
            try:
                sys.argv = ['manage.py', 'runserver', f'127.0.0.1:{port}']
                execute_from_command_line(sys.argv)
                break
            except Exception as e:
                print(f"Port {port} failed: {e}")
                continue
                
    except Exception as e:
        print(f"Server startup failed: {e}")
        print("\nTroubleshooting:")
        print("1. Check if another Django server is running")
        print("2. Try restarting your terminal")
        print("3. Check firewall settings")
