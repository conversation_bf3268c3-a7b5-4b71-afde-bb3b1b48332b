/* ===== SAFENEST AI CHATBOT STYLES ===== */

#ai-chatbot {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 10000;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Chat Widget */
.chat-widget {
    width: 380px;
    height: 600px;
    background: rgba(26, 29, 58, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 255, 255, 0.2);
    border: 1px solid rgba(0, 255, 255, 0.3);
    backdrop-filter: blur(20px);
    display: flex;
    flex-direction: column;
    transform: translateY(100%) scale(0.8);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.chat-widget.open {
    transform: translateY(0) scale(1);
    opacity: 1;
}

/* Cha<PERSON>er */
.chat-header {
    background: linear-gradient(135deg, #00ffff 0%, #8a2be2 100%);
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 20px 20px 0 0;
}

.chat-header-content {
    display: flex;
    align-items: center;
    gap: 10px;
    color: white;
    font-weight: 600;
}

.chat-header-content i {
    font-size: 1.2rem;
}

.chat-status {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.8rem;
    margin-left: 10px;
}

.status-dot {
    width: 8px;
    height: 8px;
    background: #00ff00;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.chat-controls {
    display: flex;
    gap: 5px;
}

.chat-controls button {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
}

.chat-controls button:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Chat Messages */
.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #00ffff 0%, #8a2be2 100%);
    border-radius: 3px;
}

/* Message Styles */
.message {
    display: flex;
    gap: 10px;
    animation: messageSlide 0.3s ease-out;
}

@keyframes messageSlide {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.user-message {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.bot-message .message-avatar {
    background: linear-gradient(135deg, #00ffff 0%, #8a2be2 100%);
    color: white;
}

.user-message .message-avatar {
    background: rgba(255, 255, 255, 0.1);
    color: #00ffff;
}

.message-content {
    max-width: 70%;
    background: rgba(255, 255, 255, 0.1);
    padding: 12px 16px;
    border-radius: 18px;
    color: white;
    line-height: 1.4;
}

.user-message .message-content {
    background: linear-gradient(135deg, #00ffff 0%, #8a2be2 100%);
    border-radius: 18px 18px 4px 18px;
}

.bot-message .message-content {
    border-radius: 18px 18px 18px 4px;
}

.message-content p {
    margin: 0 0 8px 0;
}

.message-content p:last-child {
    margin-bottom: 0;
}

.message-content ul {
    margin: 8px 0;
    padding-left: 20px;
}

.message-content li {
    margin: 4px 0;
}

.message-time {
    font-size: 0.7rem;
    opacity: 0.7;
    display: block;
    margin-top: 5px;
}

/* Typing Indicator */
.typing-indicator .message-content {
    padding: 12px 16px;
}

.typing-dots {
    display: flex;
    gap: 4px;
    align-items: center;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    background: #00ffff;
    border-radius: 50%;
    animation: typingDot 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typingDot {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Chat Input */
.chat-input-container {
    padding: 20px;
    border-top: 1px solid rgba(0, 255, 255, 0.2);
}

.chat-input {
    display: flex;
    gap: 10px;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 25px;
    padding: 5px;
    margin-bottom: 15px;
}

.chat-input input {
    flex: 1;
    background: none;
    border: none;
    color: white;
    padding: 12px 15px;
    font-size: 14px;
    outline: none;
}

.chat-input input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.chat-input button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.voice-btn {
    background: rgba(255, 255, 255, 0.1);
    color: #00ffff;
}

.voice-btn:hover {
    background: rgba(0, 255, 255, 0.2);
    transform: scale(1.1);
}

.voice-btn.listening {
    background: #ff4444;
    color: white;
    animation: pulse 1s infinite;
}

.send-btn {
    background: linear-gradient(135deg, #00ffff 0%, #8a2be2 100%);
    color: white;
}

.send-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(0, 255, 255, 0.3);
}

/* Quick Actions */
.quick-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.quick-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(0, 255, 255, 0.3);
    color: #00ffff;
    padding: 8px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.quick-btn:hover {
    background: rgba(0, 255, 255, 0.2);
    transform: translateY(-2px);
}

.quick-btn i {
    font-size: 0.7rem;
}

/* Chat Toggle Button */
.chat-toggle {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #00ffff 0%, #8a2be2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 8px 25px rgba(0, 255, 255, 0.3);
    transition: all 0.3s ease;
    position: relative;
    color: white;
    font-size: 1.5rem;
}

.chat-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 35px rgba(0, 255, 255, 0.4);
}

.chat-toggle.active {
    transform: scale(0.9);
}

.chat-notification {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ff4444;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: bold;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Mobile Responsiveness */
@media (max-width: 480px) {
    .chat-widget {
        width: calc(100vw - 40px);
        height: calc(100vh - 100px);
        bottom: 10px;
        right: 10px;
        left: 10px;
    }
    
    .chat-toggle {
        bottom: 10px;
        right: 10px;
    }
}
