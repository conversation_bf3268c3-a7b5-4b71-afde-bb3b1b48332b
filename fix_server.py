#!/usr/bin/env python
"""
Fix Django server listen failure issues
"""

import os
import socket
import subprocess
import sys

def check_port_availability(port=8000):
    """Check if port is available"""
    print(f"🔍 Checking port {port} availability...")
    
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('127.0.0.1', port))
            print(f"✅ Port {port} is available")
            return True
    except OSError as e:
        print(f"❌ Port {port} is in use: {e}")
        return False

def find_processes_on_port(port=8000):
    """Find processes using the port"""
    print(f"\n🔍 Finding processes on port {port}...")
    
    try:
        # Windows command to find processes on port
        result = subprocess.run(
            ['netstat', '-ano'], 
            capture_output=True, 
            text=True, 
            shell=True
        )
        
        lines = result.stdout.split('\n')
        for line in lines:
            if f':{port}' in line and 'LISTENING' in line:
                parts = line.split()
                if len(parts) >= 5:
                    pid = parts[-1]
                    print(f"📊 Process using port {port}: PID {pid}")
                    
                    # Get process name
                    try:
                        tasklist_result = subprocess.run(
                            ['tasklist', '/FI', f'PID eq {pid}'],
                            capture_output=True,
                            text=True,
                            shell=True
                        )
                        print(f"📋 Process details:\n{tasklist_result.stdout}")
                    except:
                        pass
                    
                    return pid
        
        print(f"✅ No processes found on port {port}")
        return None
        
    except Exception as e:
        print(f"❌ Error checking processes: {e}")
        return None

def kill_process_on_port(port=8000):
    """Kill process on port"""
    pid = find_processes_on_port(port)
    
    if pid:
        print(f"\n🔧 Attempting to kill process {pid}...")
        try:
            subprocess.run(['taskkill', '/F', '/PID', pid], shell=True)
            print(f"✅ Process {pid} killed")
            return True
        except Exception as e:
            print(f"❌ Failed to kill process: {e}")
            return False
    
    return True

def check_django_setup():
    """Check Django setup"""
    print("\n🔍 Checking Django setup...")
    
    try:
        # Check if manage.py exists
        if os.path.exists('manage.py'):
            print("✅ manage.py found")
        else:
            print("❌ manage.py not found")
            return False
        
        # Check Django installation
        result = subprocess.run(
            [sys.executable, '-c', 'import django; print(django.get_version())'],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print(f"✅ Django version: {result.stdout.strip()}")
        else:
            print(f"❌ Django import failed: {result.stderr}")
            return False
        
        # Check settings
        result = subprocess.run(
            [sys.executable, 'manage.py', 'check'],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print("✅ Django settings check passed")
        else:
            print(f"❌ Django settings check failed:\n{result.stderr}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking Django: {e}")
        return False

def try_different_ports():
    """Try starting server on different ports"""
    print("\n🔧 Trying different ports...")
    
    ports_to_try = [8000, 8001, 8002, 8080, 3000]
    
    for port in ports_to_try:
        print(f"\n🧪 Trying port {port}...")
        
        if check_port_availability(port):
            print(f"🚀 Starting Django server on port {port}...")
            
            try:
                # Start server in background
                process = subprocess.Popen(
                    [sys.executable, 'manage.py', 'runserver', f'127.0.0.1:{port}'],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                
                # Wait a bit to see if it starts
                import time
                time.sleep(3)
                
                if process.poll() is None:
                    print(f"✅ Server started successfully on port {port}!")
                    print(f"🌐 Visit: http://127.0.0.1:{port}/")
                    print(f"🤖 AI Assistant: http://127.0.0.1:{port}/ai/")
                    
                    # Keep server running
                    print("\n📝 Server is running. Press Ctrl+C to stop.")
                    try:
                        process.wait()
                    except KeyboardInterrupt:
                        print("\n🛑 Stopping server...")
                        process.terminate()
                    
                    return True
                else:
                    stdout, stderr = process.communicate()
                    print(f"❌ Server failed to start:")
                    print(f"📄 Output: {stdout}")
                    print(f"📄 Error: {stderr}")
            
            except Exception as e:
                print(f"❌ Error starting server: {e}")
        
        else:
            print(f"⚠️  Port {port} is not available")
    
    return False

def main():
    """Main troubleshooting function"""
    print("🔧 Django Server Troubleshooter")
    print("=" * 40)
    
    # Step 1: Check Django setup
    if not check_django_setup():
        print("\n❌ Django setup issues found. Please fix them first.")
        return
    
    # Step 2: Check port 8000
    if not check_port_availability(8000):
        print("\n🔧 Port 8000 is in use. Attempting to free it...")
        if kill_process_on_port(8000):
            print("✅ Port 8000 freed")
        else:
            print("❌ Could not free port 8000")
    
    # Step 3: Try starting server
    if not try_different_ports():
        print("\n❌ Could not start Django server on any port")
        print("\n🔧 Manual troubleshooting steps:")
        print("1. Restart your computer")
        print("2. Check firewall settings")
        print("3. Try running as administrator")
        print("4. Check antivirus software")
    
if __name__ == "__main__":
    main()
