# SafeNest Environment Configuration
# Copy this file to .env and update with your actual values

# Django Settings
SECRET_KEY=your-secret-key-here
DEBUG=True

# OpenRouter AI API Configuration
# Get your API key from: https://openrouter.ai/keys
# Using GLM-4.5-air:free model (completely free!)
OPENROUTER_API_KEY=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
OPENROUTER_MODEL=z-ai/glm-4.5-air:free

# Database Configuration (if using PostgreSQL)
# DATABASE_URL=postgresql://username:password@localhost:5432/safenest

# Redis Configuration (for Channels and Celery)
# REDIS_URL=redis://localhost:6379

# Email Configuration (for notifications)
# EMAIL_HOST=smtp.gmail.com
# EMAIL_PORT=587
# EMAIL_HOST_USER=<EMAIL>
# EMAIL_HOST_PASSWORD=your-app-password
# EMAIL_USE_TLS=True

# Security Settings (for production)
# ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
# SECURE_SSL_REDIRECT=True
# SECURE_HSTS_SECONDS=31536000
